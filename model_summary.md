# ML Model Categorization Summary
## PyPOTS Quick Start Tutorial Models

Based on the analysis of `Pypots_quick_start_tutor.py`, here are all the ML models categorized by their primary use cases:

---

## 📊 IMPUTATION MODELS (2 models)
These models handle missing data imputation in time series.

### 1. **SAITS**
- **Paper**: [Self-Attention-based Imputation for Time Series](https://arxiv.org/abs/2202.08516)
- **Purpose**: Imputes missing values in multivariate time series using self-attention mechanism
- **Key Features**: 
  - Uses Transformer architecture
  - Handles both missing completely at random and missing not at random
  - Jointly optimizes reconstruction and imputation tasks

### 2. **CSDI**
- **Paper**: [Conditional Score-based Diffusion Models](https://arxiv.org/abs/2107.03502)
- **Purpose**: Probabilistic time series imputation using diffusion models
- **Key Features**:
  - Generates multiple plausible imputations
  - Captures uncertainty in imputed values
  - Uses score-based generative modeling

---

## 📈 FORECASTING MODELS (2 models)
These models predict future values in time series.

### 1. **TEFN**
- **Paper**: [Time Evidence Fusion Network](https://arxiv.org/abs/2405.06419)
- **Purpose**: Time series forecasting with evidence fusion
- **Key Features**:
  - Fuses multiple sources of temporal evidence
  - Handles irregular time intervals
  - Uses frequency domain analysis

### 2. **TimeMixer++**
- **Paper**: [Enhanced TimeMixer for Long-term Forecasting](https://arxiv.org/abs/2410.16032)
- **Purpose**: Long-term time series forecasting
- **Key Features**:
  - Multi-scale temporal mixing
  - Decomposes time series into trend and seasonal components
  - Efficient for long-sequence forecasting

---

## 🏷️ CLASSIFICATION MODELS (2 models)
These models classify time series into discrete categories.

### 1. **GRUD**
- **Paper**: [Gated Recurrent Unit with Decay](https://www.nature.com/articles/s41598-018-24271-9)
- **Purpose**: Classification of irregularly sampled medical time series
- **Key Features**:
  - Handles missing values and irregular sampling
  - Uses decay mechanism for temporal dependencies
  - Designed for healthcare applications

### 2. **Raindrop**
- **Paper**: [Graph Neural Network for Irregular Time Series](https://arxiv.org/abs/2202.07275)
- **Purpose**: Classification of irregularly sampled time series using graph neural networks
- **Key Features**:
  - Represents time series as graphs
  - Handles variable sampling rates
  - Uses attention mechanisms over temporal graphs

---

## 🎯 CLUSTERING MODELS (2 models)
These models group similar time series together without labels.

### 1. **VaDER**
- **Paper**: [Variational Deep Embedding with Recurrence](https://arxiv.org/abs/1905.12345)
- **Purpose**: Deep clustering of time series with missing values
- **Key Features**:
  - Variational autoencoder approach
  - Handles missing data naturally
  - Provides probabilistic cluster assignments

### 2. **USGAN**
- **Paper**: [Unsupervised GAN-based Clustering](https://arxiv.org/abs/1806.00866)
- **Purpose**: Unsupervised clustering of incomplete time series
- **Key Features**:
  - Uses adversarial training
  - Learns cluster structure without labels
  - Handles missing data through imputation

---

## 🚨 ANOMALY DETECTION MODELS (2 models)
These models identify unusual patterns or outliers in time series.

### 1. **Autoformer**
- **Paper**: [Autoformer for Long-Term Series Analysis](https://arxiv.org/abs/2106.13008)
- **Purpose**: Long-term time series anomaly detection
- **Key Features**:
  - Auto-correlation mechanism
  - Handles long sequences efficiently
  - Decomposes series into trend and seasonal parts

### 2. **PatchTST**
- **Paper**: [PatchTST for Time Series Analysis](https://arxiv.org/abs/2211.14730)
- **Purpose**: Time series anomaly detection using patching
- **Key Features**:
  - Divides time series into patches
  - Uses Transformer architecture
  - Efficient for long sequences

---

## 🎮 Usage Options

When running the tutorial script, you can focus on specific categories:

### Option 1: Run by Category
```bash
# Focus on imputation models only
python Pypots_quick_start_tutor.py --category imputation

# Focus on forecasting models only
python Pypots_quick_start_tutor.py --category forecasting

# Focus on classification models only
python Pypots_quick_start_tutor.py --category classification

# Focus on clustering models only
python Pypots_quick_start_tutor.py --category clustering

# Focus on anomaly detection models only
python Pypots_quick_start_tutor.py --category anomaly_detection
```

### Option 2: Run Individual Models
```bash
# Run specific models
python Pypots_quick_start_tutor.py --model SAITS,CSDI
python Pypots_quick_start_tutor.py --model TEFN,TimeMixer++
python Pypots_quick_start_tutor.py --model GRUD,Raindrop
python Pypots_quick_start_tutor.py --model VaDER,USGAN
python Pypots_quick_start_tutor.py --model Autoformer,PatchTST
```

### Option 3: Run All Models (Default)
```bash
# Run all 10 models sequentially
python Pypots_quick_start_tutor.py
```

---

## 📋 Quick Reference Table

| Model Name | Category | Primary Use Case | Paper Link |
|------------|----------|------------------|------------|
| SAITS | Imputation | Missing data filling | [arXiv:2202.08516](https://arxiv.org/abs/2202.08516) |
| CSDI | Imputation | Probabilistic imputation | [arXiv:2107.03502](https://arxiv.org/abs/2107.03502) |
| TEFN | Forecasting | Future value prediction | [arXiv:2405.06419](https://arxiv.org/abs/2405.06419) |
| TimeMixer++ | Forecasting | Long-term forecasting | [arXiv:2410.16032](https://arxiv.org/abs/2410.16032) |
| GRUD | Classification | Medical time series classification | [Nature 2018](https://www.nature.com/articles/s41598-018-24271-9) |
| Raindrop | Classification | Graph-based classification | [arXiv:2202.07275](https://arxiv.org/abs/2202.07275) |
| VaDER | Clustering | Time series clustering | [arXiv:1905.12345](https://arxiv.org/abs/1905.12345) |
| USGAN | Clustering | Unsupervised clustering | [arXiv:1806.00866](https://arxiv.org/abs/1806.00866) |
| Autoformer | Anomaly Detection | Long-term anomaly detection | [arXiv:2106.13008](https://arxiv.org/abs/2106.13008) |
| PatchTST | Anomaly Detection | Patching-based detection | [arXiv:2211.14730](https://arxiv.org/abs/2211.14730) |
