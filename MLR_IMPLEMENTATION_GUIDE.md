# Multiple Linear Regression (MLR) Implementation Guide

## Overview

This document describes the implementation of Multiple Linear Regression (MLR) methods for well log imputation in the ML Log Prediction system. The implementation provides four MLR variants with integrated preprocessing, diagnostic capabilities, and seamless integration with the existing pipeline.

## Available MLR Models

### 1. Linear Regression
- **Model Key**: `linear_regression`
- **Description**: Standard multiple linear regression without regularization
- **Best For**: Interpretable baseline when linear relationships exist and multicollinearity is minimal
- **Assumptions**: Linearity, independence, homoscedasticity, normality, no multicollinearity

### 2. Ridge Regression
- **Model Key**: `ridge_regression`
- **Description**: Linear regression with L2 regularization
- **Best For**: Handling multicollinearity while maintaining all features
- **Regularization**: L2 penalty shrinks coefficients toward zero
- **Key Parameter**: `alpha` (regularization strength)

### 3. Lasso Regression
- **Model Key**: `lasso_regression`
- **Description**: Linear regression with L1 regularization
- **Best For**: Automatic feature selection and sparse solutions
- **Regularization**: L1 penalty can set coefficients to exactly zero
- **Key Parameters**: `alpha` (regularization strength), `max_iter`, `selection`

### 4. ElasticNet Regression
- **Model Key**: `elastic_net`
- **Description**: Linear regression combining L1 and L2 regularization
- **Best For**: Balanced feature selection and multicollinearity handling
- **Regularization**: Combines benefits of Ridge and Lasso
- **Key Parameters**: `alpha` (overall strength), `l1_ratio` (L1 vs L2 balance)

## Key Features

### Automated Preprocessing
- **Feature Scaling**: Standard, Robust, or MinMax scaling options
- **Outlier Detection**: Z-score based outlier identification and handling
- **Multicollinearity Analysis**: Variance Inflation Factor (VIF) calculation
- **Missing Value Handling**: Forward-fill and back-fill for feature gaps

### Diagnostic Capabilities
- **Model Coefficients**: Feature importance and intercept values
- **Residual Analysis**: Mean, standard deviation, and normality tests
- **VIF Analysis**: Multicollinearity detection with threshold warnings
- **Assumption Validation**: Linear regression assumption checking

### Integration Benefits
- **Seamless Pipeline**: Works with existing `impute_logs()` function
- **Consistent Interface**: Same evaluation metrics as other models
- **Performance Comparison**: Direct comparison with XGBoost, LightGBM, etc.
- **Computational Efficiency**: Very fast training and prediction

## Hyperparameters

### Common Parameters (All Models)
```python
{
    'scaling_method': 'standard',      # 'standard', 'robust', 'minmax'
    'outlier_threshold': 3.0,          # Z-score threshold (1.5-5.0)
    'vif_threshold': 10.0,             # VIF threshold (5.0-20.0)
    'handle_outliers': True,           # Enable outlier handling
    'enable_diagnostics': False,       # Enable diagnostic output
    'fit_intercept': True              # Fit intercept term
}
```

### Model-Specific Parameters

#### Ridge Regression
```python
{
    'alpha': 1.0,                      # L2 regularization strength
    'solver': 'auto'                   # Solver algorithm
}
```

#### Lasso Regression
```python
{
    'alpha': 1.0,                      # L1 regularization strength
    'max_iter': 1000,                  # Maximum iterations
    'selection': 'cyclic'              # Feature selection method
}
```

#### ElasticNet Regression
```python
{
    'alpha': 1.0,                      # Overall regularization strength
    'l1_ratio': 0.5,                   # L1 vs L2 ratio (0=Ridge, 1=Lasso)
    'max_iter': 1000,                  # Maximum iterations
    'selection': 'cyclic'              # Feature selection method
}
```

## Usage Examples

### Basic Usage
```python
from ml_core import MODEL_REGISTRY, impute_logs

# Select MLR model
model_config = MODEL_REGISTRY['ridge_regression']

# Configure hyperparameters
hparams = {
    'alpha': 0.1,
    'scaling_method': 'standard',
    'enable_diagnostics': True
}

# Create model instance
model = model_config['model_class'](**hparams)

# Run imputation
models_to_run = {'Ridge Regression': model}
result_df, model_results = impute_logs(
    df=data,
    feature_cols=['GR', 'NPHI', 'RHOB'],
    target_col='DT',
    models_to_run=models_to_run,
    well_cfg=well_config,
    prediction_mode=1
)
```

### Advanced Configuration
```python
# High regularization for noisy data
ridge_model = MODEL_REGISTRY['ridge_regression']['model_class'](
    alpha=10.0,
    scaling_method='robust',
    outlier_threshold=2.5,
    handle_outliers=True,
    enable_diagnostics=True
)

# Feature selection with Lasso
lasso_model = MODEL_REGISTRY['lasso_regression']['model_class'](
    alpha=0.01,
    max_iter=2000,
    scaling_method='standard',
    enable_diagnostics=True
)

# Balanced approach with ElasticNet
elastic_model = MODEL_REGISTRY['elastic_net']['model_class'](
    alpha=1.0,
    l1_ratio=0.7,  # More L1 (feature selection)
    scaling_method='standard',
    enable_diagnostics=True
)
```

## Diagnostic Output

When `enable_diagnostics=True`, the models provide detailed output:

### Preprocessing Diagnostics
```
🔍 MLR Preprocessing: Detected 12 outliers (2.1%)

📊 Multicollinearity Analysis (VIF):
   Feature                VIF      Status
   ----------------------------------------
   GR                    2.34    Low ✓
   NPHI                  4.67    Moderate
   RHOB                  3.21    Low ✓
   MD                    1.89    Low ✓

   ✅ No severe multicollinearity detected.
```

### Model Diagnostics
```
📈 Model Diagnostics:
   R² Score: 0.7713
   MAE: 3.7903
   RMSE: 4.8405

📊 Feature Coefficients:
   GR                  :  12.6979
   NPHI                :   8.9582
   RHOB                :  -7.0607
   MD                  :   0.4870
   Intercept           : -69.8593

🔍 Residual Analysis:
   Mean residual: -0.000000
   Std residual: 4.8405
   Normality test p-value: 0.2790
   ✅ Residuals appear normally distributed
```

## Best Practices

### Model Selection Guidelines

1. **Linear Regression**: Use when:
   - You need maximum interpretability
   - Multicollinearity is not a concern (VIF < 5)
   - You want to understand feature relationships

2. **Ridge Regression**: Use when:
   - Multicollinearity is present (VIF > 5)
   - You want to keep all features
   - Prediction accuracy is more important than sparsity

3. **Lasso Regression**: Use when:
   - You suspect some features are irrelevant
   - You want automatic feature selection
   - You prefer sparse models

4. **ElasticNet**: Use when:
   - You want both regularization and feature selection
   - You have groups of correlated features
   - You want a balanced approach

### Hyperparameter Tuning

1. **Regularization Strength (`alpha`)**:
   - Start with default (1.0)
   - Increase for more regularization (less overfitting)
   - Decrease for less regularization (more complex models)

2. **Scaling Method**:
   - `standard`: Use when features have different scales
   - `robust`: Use when outliers are present
   - `minmax`: Use when you need bounded features

3. **Outlier Handling**:
   - Enable for noisy well log data
   - Adjust threshold based on data quality
   - Monitor diagnostic output for outlier counts

## Performance Characteristics

### Computational Efficiency
- **Training Time**: Very fast (< 1 second for typical datasets)
- **Memory Usage**: Minimal (suitable for large datasets)
- **Prediction Speed**: Extremely fast
- **Scalability**: Excellent for large datasets

### Accuracy Expectations
- **Baseline Performance**: Good for linear relationships
- **Comparison**: Often competitive with tree-based models for linear patterns
- **Interpretability**: Highest among all available models
- **Robustness**: Good with proper preprocessing

## Integration with Existing Pipeline

The MLR models integrate seamlessly with the existing ML Log Prediction pipeline:

1. **Model Registry**: Automatically available in `MODEL_REGISTRY`
2. **Evaluation**: Uses same metrics (MAE, RMSE, R²) as other models
3. **Comparison**: Direct performance comparison with other models
4. **Output Format**: Consistent with existing model outputs
5. **Configuration**: Same hyperparameter configuration interface

## Dependencies

### Required
- `scikit-learn`: Core linear regression implementations
- `numpy`: Numerical computations
- `pandas`: Data manipulation
- `scipy`: Statistical functions

### Optional
- `statsmodels`: VIF calculation (graceful fallback if unavailable)
- `matplotlib`: Diagnostic plots (graceful fallback if unavailable)

## Troubleshooting

### Common Issues

1. **Convergence Warnings (Lasso/ElasticNet)**:
   - Increase `max_iter`
   - Adjust `alpha` value
   - Check feature scaling

2. **High VIF Values**:
   - Use Ridge or ElasticNet instead of Linear
   - Consider feature selection
   - Check for redundant features

3. **Poor Performance**:
   - Enable diagnostics to check assumptions
   - Try different scaling methods
   - Consider non-linear relationships (use other models)

4. **Singular Matrix Errors**:
   - Check for perfect multicollinearity
   - Remove redundant features
   - Use regularized models (Ridge/ElasticNet)

## Conclusion

The MLR implementation provides a robust, interpretable baseline for well log imputation with comprehensive preprocessing and diagnostic capabilities. It serves as an excellent starting point for analysis and comparison with more complex models, while offering valuable insights into feature relationships and data quality.
