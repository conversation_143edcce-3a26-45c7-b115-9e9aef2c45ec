"""
Base class for advanced deep learning models.
Provides common interface and utilities for PyPOTS-based models.

This module implements the foundation for all advanced deep learning models
including SAITS, BRITS, Enhanced U-Net, Transformer, and mRNN models.
"""

import torch
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, Union, List
import warnings
import math

# Try to import sklearn for metrics, with fallback
try:
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("Scikit-learn not available. Some evaluation metrics will be unavailable.")

# Try to import memory optimization utilities
try:
    from utils.memory_optimization import get_memory_optimizer
    from utils.gpu_fallback import safe_cuda_empty_cache
    MEMORY_UTILS_AVAILABLE = True
except ImportError:
    MEMORY_UTILS_AVAILABLE = False
    get_memory_optimizer = lambda: None
    safe_cuda_empty_cache = lambda: None

class BaseAdvancedModel(ABC):
    """
    Abstract base class for advanced deep learning models.
    Provides common interface and utilities for well log imputation.
    
    This class establishes the foundation for PyPOTS-based models and ensures
    consistent interface across all advanced model implementations.
    """
    
    def __init__(self, n_features: int = 4, sequence_len: int = 64, 
                 epochs: int = 50, batch_size: int = 32, 
                 learning_rate: float = 1e-3, **kwargs):
        """
        Initialize base model parameters.
        
        Args:
            n_features: Number of log features (GR, NPHI, RHOB, target)
            sequence_len: Length of input sequences (depth windows)
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for optimization
            **kwargs: Additional model-specific parameters
        """
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.is_fitted = False
        self.model = None
        
        # Store additional parameters
        self.model_params = kwargs
        
        # Training history
        self.training_history = {
            'loss': [],
            'epoch_times': [],
            'total_training_time': 0.0
        }
        
        print(f"🔧 Initialized {self.__class__.__name__} with:")
        print(f"   - Features: {n_features}")
        print(f"   - Sequence length: {sequence_len}")
        print(f"   - Epochs: {epochs}")
        print(f"   - Batch size: {batch_size}")
        print(f"   - Learning rate: {learning_rate}")
    
    @abstractmethod
    def _initialize_model(self) -> None:
        """Initialize the specific model architecture."""
        pass
    
    @abstractmethod
    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data in model-specific format."""
        pass
    
    def _prepare_pypots_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """
        Convert data to PyPOTS format.
        
        This is the standard data preparation method for PyPOTS-based models.
        
        Args:
            data: Input data tensor (batch, sequence, features)
            truth_data: Ground truth data (optional, for training)
            
        Returns:
            Dictionary in PyPOTS format
        """
        # Convert to numpy if needed
        if isinstance(data, torch.Tensor):
            data = data.cpu().numpy()
            
        if truth_data is not None:
            if isinstance(truth_data, torch.Tensor):
                truth_data = truth_data.cpu().numpy()
                
            # Create indicating mask (1 where data is observed, 0 where missing)
            indicating_mask = ~np.isnan(data)
            
            return {
                'X': data,
                'X_intact': truth_data,
                'indicating_mask': indicating_mask
            }
        else:
            # For prediction only
            indicating_mask = ~np.isnan(data)
            return {
                'X': data,
                'indicating_mask': indicating_mask
            }
    
    def _validate_input_data(self, data: torch.Tensor) -> bool:
        """
        Validate input data format and content.
        
        Args:
            data: Input data tensor
            
        Returns:
            bool: True if data is valid, False otherwise
        """
        if not isinstance(data, torch.Tensor):
            print(f"❌ Error: Expected torch.Tensor, got {type(data)}")
            return False
        
        if len(data.shape) != 3:
            print(f"❌ Error: Expected 3D tensor (batch, sequence, features), got shape {data.shape}")
            return False
        
        batch_size, seq_len, n_feat = data.shape
        
        if seq_len != self.sequence_len:
            print(f"⚠️ Warning: Sequence length mismatch. Expected {self.sequence_len}, got {seq_len}")
        
        if n_feat != self.n_features:
            print(f"⚠️ Warning: Feature count mismatch. Expected {self.n_features}, got {n_feat}")
        
        # Check for all-NaN sequences
        all_nan_sequences = torch.isnan(data).all(dim=(1, 2)).sum().item()
        if all_nan_sequences > 0:
            print(f"⚠️ Warning: {all_nan_sequences} sequences are all-NaN")
        
        return True
    
    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor, 
            epochs: Optional[int] = None, batch_size: Optional[int] = None,
            patience: int = 10, min_delta: float = 1e-4) -> None:
        """
        Train the model with GPU memory profiling, learning rate scheduling, gradient checkpointing, and early stopping.
        
        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
            patience: Early stopping patience
            min_delta: Minimum change for early stopping
        """
        import time
        import torch
        import torch.optim.lr_scheduler as lr_scheduler
        from torch.utils.checkpoint import checkpoint
        from utils.memory_optimization import MemoryOptimizer
        from torch.utils.data import DataLoader, TensorDataset
        
        # Validate input data
        if not self._validate_input_data(train_data) or not self._validate_input_data(truth_data):
            raise ValueError("Invalid input data format")
        
        # Initialize model if not already done
        if self.model is None:
            self._initialize_model()
            
        # Use provided parameters or defaults
        epochs = epochs or self.epochs
        batch_size = batch_size or self.batch_size
        
        print(f"🚀 Training {self.__class__.__name__} for {epochs} epochs...")
        print(f"   Training data shape: {train_data.shape}")
        print(f"   Missing values: {torch.isnan(train_data).sum().item()}")
        
        memory_optimizer = MemoryOptimizer()
        
        # Monitor GPU memory
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
            start_memory = torch.cuda.memory_allocated()
        
        # Record start time
        start_time = time.time()
        
        # Prepare training data as DataLoader
        train_set = self._prepare_data(train_data, truth_data)
        # Assuming train_set['X'] and train_set['X_intact'] are numpy arrays
        train_dataset = TensorDataset(torch.from_numpy(train_set['X']).float(), torch.from_numpy(train_set['X_intact']).float())
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        # Initialize optimizer and scheduler
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
        scheduler = lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)
        
        # Early stopping variables
        best_loss = float('inf')
        epochs_no_improve = 0
        
        # Training loop
        for epoch in range(epochs):
            self.model.train()
            epoch_loss = 0.0
            for batch_data, batch_truth in train_loader:
                if torch.cuda.is_available():
                    batch_data = batch_data.cuda()
                    batch_truth = batch_truth.cuda()
                
                def checkpoint_fn(inputs):
                    return self.model(inputs)
                
                outputs = checkpoint(checkpoint_fn, batch_data)
                loss = torch.nn.functional.mse_loss(outputs, batch_truth)  # Adjust loss as needed
                
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.4f}")
            
            scheduler.step(avg_loss)
            
            # Early stopping
            if avg_loss < best_loss - min_delta:
                best_loss = avg_loss
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1
                if epochs_no_improve >= patience:
                    print(f"Early stopping at epoch {epoch+1}")
                    break
        
        self.is_fitted = True
        
        # Record training time
        training_time = time.time() - start_time
        self.training_history['total_training_time'] = training_time
        
        print(f"✅ {self.__class__.__name__} training completed in {training_time:.2f} seconds!")
        
        if torch.cuda.is_available():
            peak_memory = torch.cuda.max_memory_allocated()
            print(f"Peak GPU memory: {peak_memory / 1024**2:.2f} MB")
    
    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values.
        
        Args:
            data: Input data with missing values
            
        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")
        
        # Validate input data
        if not self._validate_input_data(data):
            raise ValueError("Invalid input data format")
            
        print(f"🔮 Predicting with {self.__class__.__name__}...")
        print(f"   Input data shape: {data.shape}")
        
        # Prepare test data
        test_set = self._prepare_data(data)
        
        # Get predictions
        try:
            imputed_data = self.model.predict(test_set)

            # Handle PyPOTS output format (dict with 'imputation' key)
            if isinstance(imputed_data, dict):
                if 'imputation' in imputed_data:
                    result_array = imputed_data['imputation']
                else:
                    # Try to find the main output key
                    possible_keys = ['imputed_data', 'predictions', 'output']
                    result_array = None
                    for key in possible_keys:
                        if key in imputed_data:
                            result_array = imputed_data[key]
                            break

                    if result_array is None:
                        # Use the first array-like value
                        for value in imputed_data.values():
                            if isinstance(value, (np.ndarray, torch.Tensor)):
                                result_array = value
                                break

                        if result_array is None:
                            raise ValueError(f"Could not find imputation data in PyPOTS output: {list(imputed_data.keys())}")
            else:
                result_array = imputed_data

            # Convert back to tensor format
            if isinstance(result_array, np.ndarray):
                result = torch.from_numpy(result_array).float()
            elif isinstance(result_array, torch.Tensor):
                result = result_array.float()
            else:
                raise ValueError(f"Unexpected output type: {type(result_array)}")

            print(f"✅ Prediction completed. Output shape: {result.shape}")
            return result
            
        except Exception as e:
            print(f"❌ Prediction failed: {e}")
            raise

    def predict_with_memory_optimization(self, data: torch.Tensor,
                                       max_batch_size: Optional[int] = None,
                                       enable_fallback: bool = True) -> torch.Tensor:
        """
        Memory-optimized prediction with automatic batch processing.

        This method automatically splits large datasets into manageable chunks
        to prevent GPU out-of-memory errors.

        Args:
            data: Input data with missing values
            max_batch_size: Maximum batch size (auto-calculated if None)
            enable_fallback: Whether to fallback to CPU if GPU fails

        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")

        # Validate input data
        if not self._validate_input_data(data):
            raise ValueError("Invalid input data format")

        total_samples, seq_len, n_features = data.shape

        print(f"🧠 Memory-optimized prediction for {total_samples:,} samples")

        # Initialize memory optimizer if available
        memory_optimizer = get_memory_optimizer() if MEMORY_UTILS_AVAILABLE else None

        # Calculate optimal batch size if not provided
        if max_batch_size is None and memory_optimizer:
            model_params = {
                'd_model': getattr(self, 'd_model', 256),
                'n_layers': getattr(self, 'n_layers', 2),
                'n_heads': getattr(self, 'n_heads', 4)
            }
            max_batch_size = memory_optimizer.calculate_optimal_batch_size(
                (total_samples, seq_len, n_features), model_params
            )
        elif max_batch_size is None:
            # Fallback calculation without memory optimizer
            if torch.cuda.is_available():
                try:
                    gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                    # Rough estimation: 1GB can handle ~100-200 samples for typical models
                    max_batch_size = max(1, min(32, int(gpu_memory_gb * 50)))
                except:
                    max_batch_size = 16  # Conservative fallback
            else:
                max_batch_size = 32  # CPU can handle larger batches

        # Ensure batch size is reasonable
        max_batch_size = max(1, min(max_batch_size, total_samples))

        print(f"   • Using batch size: {max_batch_size}")
        print(f"   • Number of batches: {math.ceil(total_samples / max_batch_size)}")

        # Process in batches
        results = []
        num_batches = math.ceil(total_samples / max_batch_size)

        for batch_idx in range(num_batches):
            start_idx = batch_idx * max_batch_size
            end_idx = min(start_idx + max_batch_size, total_samples)
            batch_data = data[start_idx:end_idx]

            print(f"   📦 Processing batch {batch_idx + 1}/{num_batches} "
                  f"(samples {start_idx:,}-{end_idx-1:,})")

            try:
                # Use memory-efficient context if available
                if memory_optimizer:
                    with memory_optimizer.memory_efficient_context():
                        batch_result = self._predict_batch_with_fallback(
                            batch_data, enable_fallback
                        )
                else:
                    batch_result = self._predict_batch_with_fallback(
                        batch_data, enable_fallback
                    )

                results.append(batch_result)

                # Clear memory between batches
                if MEMORY_UTILS_AVAILABLE:
                    safe_cuda_empty_cache()

            except Exception as e:
                print(f"❌ Batch {batch_idx + 1} failed: {e}")
                if enable_fallback:
                    print("   🔄 Attempting CPU fallback for this batch...")
                    try:
                        # Force CPU processing for this batch
                        cpu_data = batch_data.cpu()
                        batch_result = self._predict_batch_cpu_fallback(cpu_data)
                        results.append(batch_result)
                        print(f"   ✅ Batch {batch_idx + 1} completed with CPU fallback")
                    except Exception as cpu_e:
                        print(f"   ❌ CPU fallback also failed: {cpu_e}")
                        raise
                else:
                    raise

        # Concatenate results
        if results:
            final_result = torch.cat(results, dim=0)
            print(f"✅ Memory-optimized prediction completed. Output shape: {final_result.shape}")
            return final_result
        else:
            raise RuntimeError("No successful batch predictions")

    def _predict_batch_with_fallback(self, batch_data: torch.Tensor,
                                   enable_fallback: bool) -> torch.Tensor:
        """
        Predict a single batch with GPU/CPU fallback.

        Args:
            batch_data: Batch of input data
            enable_fallback: Whether to enable CPU fallback

        Returns:
            Batch prediction results
        """
        try:
            # Try regular prediction first
            return self.predict(batch_data)
        except RuntimeError as e:
            if "out of memory" in str(e).lower() and enable_fallback:
                print(f"   ⚠️ GPU OOM detected, falling back to CPU for this batch")
                return self._predict_batch_cpu_fallback(batch_data.cpu())
            else:
                raise

    def _predict_batch_cpu_fallback(self, batch_data: torch.Tensor) -> torch.Tensor:
        """
        CPU fallback prediction for a single batch.

        Args:
            batch_data: Batch of input data (on CPU)

        Returns:
            Batch prediction results
        """
        # This is a simplified CPU fallback
        # In practice, you might need to temporarily move the model to CPU
                # Safely detect original device only if the model exposes parameters
        original_device = None
        if hasattr(self.model, 'parameters') and callable(getattr(self.model, 'parameters')):
            try:
                first_param = next(self.model.parameters())
                original_device = first_param.device
            except (StopIteration, TypeError):
                # The model has a parameters() method but it yields nothing (e.g., non-PyTorch models like PyPOTS)
                original_device = None

        try:
            # Move model to CPU if it's on GPU
            if original_device and original_device.type == 'cuda':
                print("   🔄 Temporarily moving model to CPU...")
                if hasattr(self.model, 'cpu'):
                    self.model.cpu()

            # Predict on CPU
            result = self.predict(batch_data)

            return result

        finally:
            # Move model back to original device
            if original_device and original_device.type == 'cuda':
                try:
                    if hasattr(self.model, 'to'):
                        self.model.to(original_device)
                    print("   🔄 Model moved back to GPU")
                except Exception as e:
                    print(f"   ⚠️ Could not move model back to GPU: {e}")
                    print("   💡 Model will remain on CPU for subsequent operations")
    
    def evaluate_imputation(self, original_data: torch.Tensor, 
                          imputed_data: torch.Tensor, 
                          missing_mask: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """
        Evaluate imputation performance.
        
        Args:
            original_data: Original data with missing values
            imputed_data: Imputed data
            missing_mask: Mask indicating originally missing values (optional)
            
        Returns:
            Dictionary with evaluation metrics
        """
        if not SKLEARN_AVAILABLE:
            print("⚠️ Scikit-learn not available. Returning basic metrics only.")
            return {'mae': float('inf'), 'rmse': float('inf'), 'r2': -float('inf')}
        
        # Convert to numpy if needed
        if isinstance(original_data, torch.Tensor):
            original_data = original_data.cpu().numpy()
        if isinstance(imputed_data, torch.Tensor):
            imputed_data = imputed_data.cpu().numpy()
        if missing_mask is not None and isinstance(missing_mask, torch.Tensor):
            missing_mask = missing_mask.cpu().numpy()
            
        # Flatten for evaluation
        original_flat = original_data.flatten()
        imputed_flat = imputed_data.flatten()
        
        # Create evaluation mask
        if missing_mask is not None:
            missing_flat = missing_mask.flatten()
            # Evaluate only on originally non-missing values
            valid_mask = ~np.isnan(original_flat) & ~np.isnan(imputed_flat) & ~missing_flat
        else:
            # Evaluate on all non-NaN values
            valid_mask = ~np.isnan(original_flat) & ~np.isnan(imputed_flat)
        
        if valid_mask.sum() == 0:
            print("⚠️ No valid data points for evaluation")
            return {'mae': float('inf'), 'rmse': float('inf'), 'r2': -float('inf')}
            
        y_true = original_flat[valid_mask]
        y_pred = imputed_flat[valid_mask]
        
        # Calculate metrics
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        
        print(f"📊 Evaluation metrics:")
        print(f"   MAE: {mae:.4f}")
        print(f"   RMSE: {rmse:.4f}")
        print(f"   R²: {r2:.4f}")
        print(f"   Valid points: {valid_mask.sum()}")
        
        return {'mae': mae, 'rmse': rmse, 'r2': r2, 'valid_points': int(valid_mask.sum())}
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information and parameters."""
        info = {
            'model_name': self.__class__.__name__,
            'model_type': 'advanced_deep_learning',
            'n_features': self.n_features,
            'sequence_len': self.sequence_len,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate,
            'is_fitted': self.is_fitted,
            'additional_params': self.model_params,
            'training_history': self.training_history
        }
        
        # Add model-specific information if available
        if hasattr(self, 'get_params'):
            try:
                info['model_params'] = self.get_params()
            except Exception as e:
                warnings.warn(f"Could not retrieve model params: {e}")

        return info
    
    def save_model(self, filepath: str) -> None:
        """
        Save the trained model to file.
        
        Args:
            filepath: Path to save the model
        """
        if not self.is_fitted:
            raise RuntimeError("Cannot save unfitted model")
        
        # This is a placeholder - actual implementation depends on the specific model
        print(f"💾 Saving {self.__class__.__name__} to {filepath}")
        print("⚠️ Model saving not implemented in base class")
    
    def load_model(self, filepath: str) -> None:
        """
        Load a trained model from file.
        
        Args:
            filepath: Path to load the model from
        """
        # This is a placeholder - actual implementation depends on the specific model
        print(f"📂 Loading {self.__class__.__name__} from {filepath}")
        print("⚠️ Model loading not implemented in base class")
    
    def __repr__(self) -> str:
        """String representation of the model."""
        status = "fitted" if self.is_fitted else "unfitted"
        return (f"{self.__class__.__name__}("
                f"n_features={self.n_features}, "
                f"sequence_len={self.sequence_len}, "
                f"epochs={self.epochs}, "
                f"status={status})")

# Utility functions for advanced models
def validate_model_compatibility(model_class, required_methods: list = None) -> bool:
    """
    Validate that a model class is compatible with the advanced models interface.
    
    Args:
        model_class: Model class to validate
        required_methods: List of required method names
        
    Returns:
        bool: True if compatible, False otherwise
    """
    if required_methods is None:
        required_methods = ['fit', 'predict']
    
    for method in required_methods:
        if not hasattr(model_class, method):
            print(f"❌ Model {model_class.__name__} missing required method: {method}")
            return False
    
    print(f"✅ Model {model_class.__name__} is compatible")
    return True

def create_model_summary(models: Dict[str, BaseAdvancedModel]) -> Dict[str, Any]:
    """
    Create a summary of multiple models.
    
    Args:
        models: Dictionary of model name to model instance
        
    Returns:
        Dictionary with model summary information
    """
    summary = {
        'total_models': len(models),
        'fitted_models': 0,
        'model_details': {}
    }
    
    for name, model in models.items():
        if isinstance(model, BaseAdvancedModel):
            summary['model_details'][name] = model.get_model_info()
            if model.is_fitted:
                summary['fitted_models'] += 1
        else:
            summary['model_details'][name] = {'error': 'Not a BaseAdvancedModel instance'}
    
    return summary
