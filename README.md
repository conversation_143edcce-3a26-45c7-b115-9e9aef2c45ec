# ML Log Prediction

This project is a machine learning-based log prediction system designed for well log data analysis and prediction. The system provides a comprehensive workflow for loading, processing, and predicting well log data using various machine learning models.

## Features

- **Data Loading**: Load LAS (Log ASCII Standard) files from a user-selected directory, handling multiple wells and log curves.
- **Configuration**: Select feature and target logs, set up training/prediction strategies, and configure model hyperparameters.
- **Data Processing**: Clean data, perform quality control, and separate wells for training and validation.
- **Machine Learning**: Supports multiple ML models, including XGBoost, LightGBM, and a suite of Multiple Linear Regression (MLR) models. It allows for batch execution of multiple models and performance comparison.
- **Output & Visualization**: Save results, visualize performance metrics, and generate quality control reports.

## Models

The project includes a variety of machine learning models:

- **Gradient Boosting Models**: XGBoost and LightGBM for high-performance predictions.
- **Multiple Linear Regression (MLR) Models**:
    - **Linear Regression**: Standard MLR without regularization.
    - **Ridge Regression**: L2 regularization for handling multicollinearity.
    - **Lasso Regression**: L1 regularization for feature selection.
    - **ElasticNet Regression**: A combination of L1/L2 regularization.

## Getting Started

1.  **Install Dependencies**: Make sure you have Python 3.x installed, then install the required packages from `requirements.txt`:
    ```bash
    pip install -r requirements.txt
    ```
2.  **Run the Application**: Execute the main script:
    ```bash
    python main.py
    ```
3.  **Follow Prompts**: The application will guide you through the process of selecting input LAS files, configuring model parameters, running predictions, and viewing the results.

## Usage Examples

Here's how you can use one of the Multiple Linear Regression models:

```python
# Select and configure a Ridge Regression model
model_config = MODEL_REGISTRY['ridge_regression']
hparams = {
    'alpha': 0.1,
    'scaling_method': 'standard',
    'enable_diagnostics': True
}

# Create and run the model
model = model_config['model_class'](**hparams)
models_to_run = {'Ridge Regression': model}
result_df, model_results = impute_logs(
    df=data,
    feature_cols=['GR', 'NPHI', 'RHOB'],
    target_col='DT',
    models_to_run=models_to_run,
    well_cfg=well_config,
    prediction_mode=1
)
```

## Directory Structure

```
.
├── main.py                # Main application entry point
├── data_handler.py        # Data loading and preprocessing
├── config_handler.py      # Configuration management
├── ml_core.py             # Core ML functionality
├── reporting.py           # Reporting and visualization
├── mlr_utils.py           # Utilities for MLR models
├── requirements.txt       # Project dependencies
├── tests/                 # Test files
└── docs/                  # Documentation
```

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue.
