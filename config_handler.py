import os
import tkinter as tk
from tkinter import filedialog, messagebox
from ml_core import MODEL_REGISTRY

AUTO_MODE = True

def console_select(options, prompt, default=None, multiple=False):
    print(f"\n{prompt}")
    for i,opt in enumerate(options,1):
        print(f"  {i}. {opt}")
    if default:
        print(f"Default: {default}")
    if AUTO_MODE:
        return default if not multiple else (default if isinstance(default, list) else [default])
    while True:
        choice = input("Selection: ").strip()
        if not choice and default:
            return default if not multiple else (default if isinstance(default, list) else [default])
        if multiple and choice.lower()=='all':
            return options
        try:
            idxs = [int(x) for x in choice.split(',')]
            sel = [options[i-1] for i in idxs]
            return sel if multiple else sel[0]
        except:
            print("Invalid input.")

def select_las_files_dialog():
    """Open a file dialog to select multiple LAS files."""
    # Hide the main tkinter window
    root = tk.Tk()
    root.withdraw()

    # Open file dialog for multiple LAS files
    file_paths = filedialog.askopenfilenames(
        title="Select LAS Files",
        filetypes=[
            ("LAS files", "*.las"),
            ("All files", "*.*")
        ],
        multiple=True
    )

    root.destroy()

    if not file_paths:
        print("No files selected.")
        return []

    print(f"Selected {len(file_paths)} LAS files:")
    for i, path in enumerate(file_paths, 1):
        print(f"  {i}. {os.path.basename(path)}")

    return list(file_paths)

def get_io_paths():
    """Get input and output paths. Offers choice between directory or file selection."""
    print("\nChoose input method:")
    print("1. Select directory containing LAS files")
    print("2. Select individual LAS files (GUI dialog)")

    while True:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            inp = input("Input LAS directory: ").strip()
            break
        elif choice == "2":
            print("Opening file selection dialog...")
            selected_files = select_las_files_dialog()
            if selected_files:
                inp = selected_files  # Return list of file paths instead of directory
                break
            else:
                print("No files selected. Please try again.")
                continue
        else:
            print("Invalid choice. Please enter 1 or 2.")

    out = input("Output directory: ").strip()
    return inp, out

def get_input_files():
    """Get input LAS files using file dialog."""
    print("Select LAS files using the file dialog...")
    selected_files = select_las_files_dialog()

    if not selected_files:
        print("No files selected. Exiting.")
        return None

    return selected_files

def select_output_directory():
    """Open a directory dialog to select output directory."""
    root = tk.Tk()
    root.withdraw()

    output_dir = filedialog.askdirectory(
        title="Select Output Directory"
    )

    root.destroy()

    if not output_dir:
        print("No output directory selected.")
        return None

    print(f"Selected output directory: {output_dir}")
    return output_dir

def get_io_paths_simple():
    """Simple version that directly opens file dialog for LAS selection."""
    print("Select LAS files using the file dialog...")
    selected_files = select_las_files_dialog()

    if not selected_files:
        print("No files selected. Exiting.")
        return None, None

    out = input("Output directory: ").strip()
    return selected_files, out

def configure_log_selection(logs):
    feats = console_select(logs, "Select feature logs (comma separated indexes)", multiple=True, default=logs[:4])
    tlist = [l for l in logs if l not in feats]
    tgt = console_select(tlist, "Select target log", default=tlist[0])
    return feats, tgt

def configure_well_separation(wells):
    mode = console_select(['mixed','separated'], "Training/prediction mode?", default='mixed')
    if mode=='mixed':
        return {'mode':'mixed','training_wells':wells,'prediction_wells':wells}
    tr = console_select(wells, "Training wells", multiple=True)
    pr = [w for w in wells if w not in tr]
    pr = console_select(pr, "Prediction wells", multiple=True, default=pr)
    return {'mode':'separated','training_wells':tr,'prediction_wells':pr}

def get_prediction_mode():
    mode = console_select(['1','2','3'], "Prediction mode: 1 fill-missing, 2 CV, 3 full", default='1')
    return int(mode)

def configure_hyperparameters():
    params = {}
    for k,m in MODEL_REGISTRY.items():
        params[k] = {**m['fixed_params']}
        for p,meta in m['hyperparameters'].items():
            params[k][p] = meta['default']
    return params
